#!/bin/bash
#==============================================================================
# LaTeX Report Compilation Script for Unix/Linux/macOS
# This script compiles the LaTeX report template with proper bibliography
#==============================================================================

echo "Starting LaTeX compilation process..."
echo

# Check if the main .tex file exists
if [ ! -f "report_template.tex" ]; then
    echo "ERROR: report_template.tex not found!"
    echo "Please ensure the LaTeX template file is in the current directory."
    exit 1
fi

# Check if the bibliography file exists
if [ ! -f "references.bib" ]; then
    echo "WARNING: references.bib not found!"
    echo "Bibliography will not be processed."
    echo
fi

echo "Step 1: First LaTeX compilation..."
pdflatex -interaction=nonstopmode report_template.tex
if [ $? -ne 0 ]; then
    echo "ERROR: First LaTeX compilation failed!"
    echo "Check the .log file for errors."
    exit 1
fi

echo "Step 2: Processing bibliography..."
if [ -f "references.bib" ]; then
    bibtex report_template
    if [ $? -ne 0 ]; then
        echo "WARNING: Bibliography processing failed!"
        echo "Continuing without bibliography..."
    fi
else
    echo "Skipping bibliography processing (no .bib file found)"
fi

echo "Step 3: Second LaTeX compilation..."
pdflatex -interaction=nonstopmode report_template.tex
if [ $? -ne 0 ]; then
    echo "ERROR: Second LaTeX compilation failed!"
    exit 1
fi

echo "Step 4: Final LaTeX compilation..."
pdflatex -interaction=nonstopmode report_template.tex
if [ $? -ne 0 ]; then
    echo "ERROR: Final LaTeX compilation failed!"
    exit 1
fi

echo
echo "==============================================="
echo "Compilation completed successfully!"
echo "Output file: report_template.pdf"
echo "==============================================="
echo

# Clean up auxiliary files (optional)
read -p "Do you want to clean up auxiliary files? (y/n): " cleanup
if [[ $cleanup =~ ^[Yy]$ ]]; then
    echo "Cleaning up auxiliary files..."
    rm -f *.aux *.log *.bbl *.blg *.toc *.out *.lof *.lot
    echo "Cleanup completed."
fi

echo
echo "Opening PDF file..."
if [ -f "report_template.pdf" ]; then
    # Try different PDF viewers based on the system
    if command -v xdg-open > /dev/null; then
        xdg-open report_template.pdf
    elif command -v open > /dev/null; then
        open report_template.pdf
    elif command -v evince > /dev/null; then
        evince report_template.pdf &
    elif command -v okular > /dev/null; then
        okular report_template.pdf &
    else
        echo "PDF created successfully, but no PDF viewer found to open it automatically."
        echo "Please open report_template.pdf manually."
    fi
else
    echo "ERROR: PDF file was not created!"
    exit 1
fi
