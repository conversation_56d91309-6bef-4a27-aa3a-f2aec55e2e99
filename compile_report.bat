@echo off
REM ==============================================================================
REM LaTeX Report Compilation Script for Windows
REM This script compiles the LaTeX report template with proper bibliography
REM ==============================================================================

echo Starting LaTeX compilation process...
echo.

REM Check if the main .tex file exists
if not exist "report_template.tex" (
    echo ERROR: report_template.tex not found!
    echo Please ensure the LaTeX template file is in the current directory.
    pause
    exit /b 1
)

REM Check if the bibliography file exists
if not exist "references.bib" (
    echo WARNING: references.bib not found!
    echo Bibliography will not be processed.
    echo.
)

echo Step 1: First LaTeX compilation...
pdflatex -interaction=nonstopmode report_template.tex
if %errorlevel% neq 0 (
    echo ERROR: First LaTeX compilation failed!
    echo Check the .log file for errors.
    pause
    exit /b 1
)

echo Step 2: Processing bibliography...
if exist "references.bib" (
    bibtex report_template
    if %errorlevel% neq 0 (
        echo WARNING: Bibliography processing failed!
        echo Continuing without bibliography...
    )
) else (
    echo Skipping bibliography processing (no .bib file found)
)

echo Step 3: Second LaTeX compilation...
pdflatex -interaction=nonstopmode report_template.tex
if %errorlevel% neq 0 (
    echo ERROR: Second LaTeX compilation failed!
    pause
    exit /b 1
)

echo Step 4: Final LaTeX compilation...
pdflatex -interaction=nonstopmode report_template.tex
if %errorlevel% neq 0 (
    echo ERROR: Final LaTeX compilation failed!
    pause
    exit /b 1
)

echo.
echo ===============================================
echo Compilation completed successfully!
echo Output file: report_template.pdf
echo ===============================================
echo.

REM Clean up auxiliary files (optional)
set /p cleanup="Do you want to clean up auxiliary files? (y/n): "
if /i "%cleanup%"=="y" (
    echo Cleaning up auxiliary files...
    del *.aux *.log *.bbl *.blg *.toc *.out *.lof *.lot 2>nul
    echo Cleanup completed.
)

echo.
echo Opening PDF file...
if exist "report_template.pdf" (
    start report_template.pdf
) else (
    echo ERROR: PDF file was not created!
)

pause
