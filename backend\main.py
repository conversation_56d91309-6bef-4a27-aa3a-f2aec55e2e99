
import os
import uuid
import logging
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List

from fastapi import FastAPI, UploadFile, File, HTTPException, status, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel
import uvicorn

# Configuration et base de données
from core.config import settings
from core.database import get_db, Session, init_database

# Services
from services.enhanced_ocr_service import enhanced_ocr_service
from services.intelligent_grading_service import intelligent_grading_service
from services.manual_review_service import manual_review_service
from services.audit_service import audit_service

# Configuration du logging
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(settings.log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("auto-grade-scribe")

# Modèles Pydantic
class HealthResponse(BaseModel):
    status: str
    version: str
    timestamp: str
    services: Dict[str, bool]

class UploadResponse(BaseModel):
    success: bool
    file_id: str
    filename: str
    message: str

class OCRRequest(BaseModel):
    file_id: str
    content_type: str = "mixed"
    force_reprocess: bool = False

class GradingRequest(BaseModel):
    file_id: str
    exam_type: str
    correct_answers: Dict[str, str]
    grading_config: Optional[Dict[str, Any]] = None

class ReviewRequest(BaseModel):
    grading_result_id: str
    reviewed_score: float
    review_comments: str
    changes_made: Dict[str, Any]

# Créer l'application FastAPI
app = FastAPI(
    title="Auto Grade Scribe Open-Source",
    description="Application de correction automatique d'examens avec IA open-source",
    version="4.0.0"
)

# Configuration CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.get_cors_origins(),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Initialisation au démarrage"""
    logger.info("🚀 Démarrage d'Auto-Grade Scribe Open-Source v4.0.0")
    
    # Initialiser la base de données
    try:
        init_database()
        logger.info("✅ Base de données initialisée")
    except Exception as e:
        logger.error(f"❌ Erreur initialisation base de données: {e}")
    
    # Vérifier les services
    services_status = {
        "ocr": enhanced_ocr_service.check_health(),
        "grading": intelligent_grading_service.check_health(),
        "review": manual_review_service.check_health(),
        "audit": audit_service.check_health()
    }
    
    for service, status in services_status.items():
        if status:
            logger.info(f"✅ Service {service} opérationnel")
        else:
            logger.warning(f"⚠️ Service {service} non disponible")

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Vérification de santé de l'application"""
    services_status = {
        "ocr": enhanced_ocr_service.check_health(),
        "grading": intelligent_grading_service.check_health(),
        "review": manual_review_service.check_health(),
        "audit": audit_service.check_health()
    }
    
    return HealthResponse(
        status="healthy" if all(services_status.values()) else "degraded",
        version="4.0.0",
        timestamp=datetime.utcnow().isoformat(),
        services=services_status
    )

@app.post("/api/upload", response_model=UploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Upload d'un fichier d'examen"""
    try:
        # Vérifier la taille du fichier
        if file.size > settings.max_file_size:
            raise HTTPException(
                status_code=413,
                detail=f"Fichier trop volumineux. Taille max: {settings.max_file_size} bytes"
            )
        
        # Générer un ID unique
        file_id = str(uuid.uuid4())
        
        # Sauvegarder le fichier
        file_path = Path(settings.upload_directory) / f"{file_id}_{file.filename}"
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Enregistrer en base de données
        from core.database import UploadedFile
        db_file = UploadedFile(
            id=file_id,
            filename=f"{file_id}_{file.filename}",
            original_filename=file.filename,
            file_path=str(file_path),
            file_size=file.size,
            content_type=file.content_type,
            uploaded_by="system"  # TODO: Utiliser l'utilisateur authentifié
        )
        db.add(db_file)
        db.commit()
        
        # Audit
        await audit_service.log_action(
            action="file_upload",
            resource_type="file",
            resource_id=file_id,
            details={"filename": file.filename, "size": file.size}
        )
        
        logger.info(f"✅ Fichier uploadé: {file.filename} (ID: {file_id})")
        
        return UploadResponse(
            success=True,
            file_id=file_id,
            filename=file.filename,
            message="Fichier uploadé avec succès"
        )
        
    except Exception as e:
        logger.error(f"❌ Erreur upload fichier: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v3/ocr/enhanced")
async def process_ocr(
    request: OCRRequest,
    db: Session = Depends(get_db)
):
    """Traitement OCR avec providers open-source"""
    try:
        # Vérifier que le fichier existe
        from core.database import UploadedFile
        db_file = db.query(UploadedFile).filter(UploadedFile.id == request.file_id).first()
        if not db_file:
            raise HTTPException(status_code=404, detail="Fichier non trouvé")
        
        # Traitement OCR
        result = await enhanced_ocr_service.process_file_enhanced(
            file_path=db_file.file_path,
            content_type=request.content_type,
            force_reprocess=request.force_reprocess
        )
        
        # Enregistrer le résultat
        from core.database import OCRResult
        db_result = OCRResult(
            file_id=request.file_id,
            provider_used=result.get("provider_used", "unknown"),
            extracted_text=result.get("extracted_text", ""),
            confidence=result.get("confidence", 0.0),
            processing_time=result.get("processing_time", 0.0),
            metadata=result.get("metadata", {})
        )
        db.add(db_result)
        db.commit()
        
        # Audit
        await audit_service.log_action(
            action="ocr_processing",
            resource_type="file",
            resource_id=request.file_id,
            details={"provider": result.get("provider_used"), "confidence": result.get("confidence")}
        )
        
        logger.info(f"✅ OCR traité: {request.file_id} avec {result.get('provider_used')}")
        
        return JSONResponse(content={
            "success": True,
            "file_id": request.file_id,
            **result
        })
        
    except Exception as e:
        logger.error(f"❌ Erreur OCR: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v3/grade/intelligent")
async def process_grading(
    request: GradingRequest,
    db: Session = Depends(get_db)
):
    """Correction intelligente avec IA open-source"""
    try:
        # Vérifier que le fichier existe
        from core.database import UploadedFile
        db_file = db.query(UploadedFile).filter(UploadedFile.id == request.file_id).first()
        if not db_file:
            raise HTTPException(status_code=404, detail="Fichier non trouvé")
        
        # Traitement de correction
        result = await intelligent_grading_service.grade_exam_intelligent(
            exam_id=request.file_id,
            exam_type=request.exam_type,
            correct_answers=request.correct_answers,
            config=request.grading_config or {}
        )
        
        # Enregistrer le résultat
        from core.database import GradingResult
        db_result = GradingResult(
            file_id=request.file_id,
            exam_id=request.file_id,
            student_answers=result.get("student_answers", {}),
            grading_details=result.get("grading_result", {}),
            final_score=result.get("final_score", {}),
            feedback=result.get("feedback", {}),
            requires_manual_review=result.get("requires_manual_review", False),
            processing_time=result.get("processing_time", 0.0)
        )
        db.add(db_result)
        db.commit()
        
        # Audit
        await audit_service.log_action(
            action="intelligent_grading",
            resource_type="exam",
            resource_id=request.file_id,
            details={"exam_type": request.exam_type, "score": result.get("final_score", {})}
        )
        
        logger.info(f"✅ Correction traitée: {request.file_id}")
        
        return JSONResponse(content={
            "success": True,
            "file_id": request.file_id,
            **result
        })
        
    except Exception as e:
        logger.error(f"❌ Erreur correction: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
