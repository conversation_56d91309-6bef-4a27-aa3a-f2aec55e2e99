"use client"

import React, { useState, useCallback } from 'react';
import { Upload, X, FileText, Check, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { useToast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/providers/auth-provider';

// API URL from environment or default
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000';

interface FileUploadProps {
  onUploadComplete?: (resultId: string) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ onUploadComplete }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [processing, setProcessing] = useState(false);
  const [fileId, setFileId] = useState<string | null>(null);
  // Default to Handwritten since this component is now only for handwritten exams
  const examType = 'Handwritten';
  const { toast } = useToast();
  const router = useRouter();
  const { accessToken } = useAuth();

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!isDragging) setIsDragging(true);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const newFiles = Array.from(e.dataTransfer.files);
    addFiles(newFiles);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      addFiles(newFiles);
    }
  };

  const addFiles = (newFiles: File[]) => {
    // Only accept PDFs and images
    const validFiles = newFiles.filter(file =>
      file.type.includes('pdf') || file.type.includes('image/')
    );

    if (validFiles.length !== newFiles.length) {
      toast({
        title: "Invalid file(s)",
        description: "Only PDF and image files are accepted",
        variant: "destructive"
      });
    }

    if (validFiles.length > 0) {
      setFiles(prev => [...prev, ...validFiles]);
    }
  };

  const removeFile = (index: number) => {
    setFiles(files.filter((_, i) => i !== index));
  };

  const handleUpload = async () => {
    if (files.length === 0) return;

    setUploading(true);
    let uploadedFileId = null;

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch(`${API_URL}/api/upload`, {
          method: 'POST',
          body: formData
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.detail || `Upload failed with status: ${response.status}`);
        }

        const data = await response.json();
        uploadedFileId = data.file_id;
        setProgress(((i + 1) / files.length) * 100);
      }

      // Set processing state after upload loop completes
      setUploading(false);
      if (uploadedFileId) {
        setProcessing(true); // Show processing indicator
        setProgress(0); // Reset progress for processing step

        // Appel réel à l'API pour le traitement
        try {
          console.log("Sending grading request to backend...");

          // Appel à l'API sans authentification
          console.log(`Sending grading request to ${API_URL}/api/grade/noauth with file_id: ${uploadedFileId}`);
          const gradeResponse = await fetch(`${API_URL}/api/grade/noauth`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              file_id: uploadedFileId,
              exam_type: examType,
              exam_template: {}
            })
          });

          console.log(`Grade response status: ${gradeResponse.status}`);

          // Récupérer les données de la réponse
          let responseData: any = {};
          try {
            responseData = await gradeResponse.json();
            console.log("Grade response data:", responseData);
          } catch (e) {
            console.error("Failed to parse grade response:", e);
          }

          // Vérifier si la réponse est OK
          if (!gradeResponse.ok) {
            throw new Error(
              (responseData && responseData.detail) ? responseData.detail :
              `Grading failed with status: ${gradeResponse.status}`
            );
          }

          // Pour les examens manuscrits, utilisons l'API d'extraction de texte
          if (examType === 'Handwritten') {
            console.log("Extracting text from handwritten exam...");

            try {
              // Construire le chemin du fichier
              const fileExtension = file.name.substring(file.name.lastIndexOf('.'));
              const filePath = `uploads/${uploadedFileId}${fileExtension}`;
              console.log(`Attempting to extract text from file: ${filePath}`);

              // Appel à l'API d'extraction de texte sans authentification
              const extractResponse = await fetch(`${API_URL}/api/ocr/${uploadedFileId}/noauth`, {
                method: 'GET',
                headers: {
                  'Content-Type': 'application/json'
                }
              });

              console.log(`Text extraction response status: ${extractResponse.status}`);

              // Traiter la réponse
              let extractData = {};
              try {
                extractData = await extractResponse.json();
                console.log("Text extraction response data:", extractData);

                if (extractResponse.ok && extractData.success) {
                  toast({
                    title: "Extraction de texte réussie",
                    description: "Le texte a été extrait avec succès de l'image.",
                    variant: "default"
                  });
                } else {
                  console.warn("Text extraction failed:", extractData.error || "Unknown error");
                  toast({
                    title: "Extraction de texte partielle",
                    description: "L'extraction du texte a rencontré des difficultés. Les résultats peuvent être incomplets.",
                    variant: "warning"
                  });
                }
              } catch (parseError) {
                console.error("Failed to parse extraction response:", parseError);
              }
            } catch (extractError) {
              console.error("Error during text extraction:", extractError);
              toast({
                title: "Échec de l'extraction",
                description: "Impossible d'extraire le texte de l'image. Veuillez réessayer.",
                variant: "destructive"
              });
            }
          }
        } catch (error) {
          console.error("Error during grading simulation:", error);
          throw new Error("Échec de l'analyse. Veuillez réessayer.");
        }

        // Grading successful
        toast({
          title: "Correction terminée",
          description: "Le fichier a été analysé et corrigé avec succès.",
          variant: "default"
        });

        setProcessing(false); // Hide processing indicator

        // Navigate or call callback
        if (onUploadComplete) {
          onUploadComplete(uploadedFileId);
        } else {
          router.push(`/results/${uploadedFileId}`);
        }
      } else {
        throw new Error("Upload succeeded but no file ID was returned.");
      }

    } catch (error) {
      console.error('Processing error:', error);
      toast({
        title: "Processing failed",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive"
      });
    } finally {
      setUploading(false);
      setProcessing(false);
      setProgress(0);
    }
  };

  return (
    <div className="w-full space-y-4">
      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
          isDragging ? "border-primary bg-primary/5" : "border-gray-300 dark:border-gray-700",
        )}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="flex flex-col items-center justify-center space-y-4">
          <div className="p-4 bg-secondary rounded-full">
            <Upload className="h-8 w-8 text-primary" />
          </div>
          <div>
            <h3 className="text-lg font-medium">Drag & drop exam files</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Upload PDFs or images of exams to be graded
            </p>
          </div>
          <div>
            <label htmlFor="file-upload">
              <Button variant="outline" className="mt-2" onClick={() => document.getElementById('file-upload')?.click()}>
                Select Files
              </Button>
              <input
                id="file-upload"
                type="file"
                accept=".pdf,image/*"
                multiple
                onChange={handleFileChange}
                className="hidden"
              />
            </label>
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            PDF or images up to 10MB
          </div>
        </div>
      </div>

      {files.length > 0 && (
        <div className="space-y-3">
          <h3 className="font-medium">Files to upload ({files.length})</h3>
          {files.map((file, index) => (
            <Card key={`${file.name}-${index}`} className="overflow-hidden">
              <CardContent className="p-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-secondary rounded-md">
                      <FileText className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium truncate max-w-[200px] sm:max-w-[400px]">{file.name}</p>
                      <p className="text-xs text-gray-500">{(file.size / 1024 / 1024).toFixed(2)} MB</p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" onClick={() => removeFile(index)} disabled={uploading || processing}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}

          {(uploading || processing) ? (
            <div className="space-y-2">
              <Progress value={uploading ? progress : processing ? 100 : 0} />
              <p className="text-sm text-center text-gray-500">
                {uploading ? `Téléchargement... ${Math.round(progress)}%` :
                 processing ? 'Analyse avec IA en cours... Cette opération peut prendre quelques instants' : ''}
              </p>
              <div className="flex justify-center mt-2">
                <Loader2 className="h-5 w-5 animate-spin text-primary" />
              </div>
              {processing && (
                <div className="text-center text-sm mt-2">
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800">
                    <h4 className="font-medium text-blue-700 dark:text-blue-300 mb-2">Processus de correction professionnelle</h4>
                    <div className="space-y-3 text-left">
                      <div className="flex items-center">
                        <div className="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center mr-2">
                          <Check className="h-3 w-3 text-green-600" />
                        </div>
                        <p className="text-sm">Prétraitement du document</p>
                      </div>
                      <div className="flex items-center">
                        <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                          <Loader2 className="h-3 w-3 animate-spin text-blue-600" />
                        </div>
                        <p className="text-sm">Extraction du texte (OCR)</p>
                      </div>
                      <div className="flex items-center opacity-60">
                        <div className="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-2">
                          <span className="h-3 w-3 text-gray-500">3</span>
                        </div>
                        <p className="text-sm">Analyse par Intelligence Artificielle</p>
                      </div>
                      <div className="flex items-center opacity-60">
                        <div className="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-2">
                          <span className="h-3 w-3 text-gray-500">4</span>
                        </div>
                        <p className="text-sm">Finalisation et notation</p>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-4">Le temps de traitement dépend de la complexité du document</p>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <Button onClick={handleUpload} className="w-full" disabled={files.length === 0}>
              {files.length > 1 ? `Télécharger et analyser ${files.length} fichiers` : "Télécharger et analyser le fichier"}
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default FileUpload;
