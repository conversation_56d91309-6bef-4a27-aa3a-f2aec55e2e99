import type { Metadata } from 'next'
import './globals.css'
import Providers from './providers'
import { inter } from './fonts'

export const metadata: Metadata = {
  title: 'GradeGenius',
  description: 'Système intelligent de correction automatique de documents',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  )
}